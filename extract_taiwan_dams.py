#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台湾水坝数据提取器
从维基百科页面提取台湾水坝的详细信息，包括坐标数据
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
import json
from typing import List, Dict, Optional

class TaiwanDamExtractor:
    def __init__(self):
        self.url = "https://zh.wikipedia.org/wiki/%E5%8F%B0%E7%81%A3%E6%B0%B4%E5%A3%A9%E5%88%97%E8%A1%A8"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def fetch_page(self) -> BeautifulSoup:
        """获取维基百科页面内容"""
        try:
            response = self.session.get(self.url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except Exception as e:
            print(f"获取页面失败: {e}")
            return None
    
    def extract_coordinates(self, coord_text: str) -> Optional[Dict[str, float]]:
        """从坐标文本中提取经纬度"""
        if not coord_text:
            return None
        
        # 匹配坐标格式：24°42′11″N 121°45′12″E
        pattern = r'(\d+)°(\d+)′(\d+)″N\s+(\d+)°(\d+)′(\d+)″E'
        match = re.search(pattern, coord_text)
        
        if match:
            lat_deg, lat_min, lat_sec, lon_deg, lon_min, lon_sec = map(int, match.groups())
            
            # 转换为十进制度数
            latitude = lat_deg + lat_min/60 + lat_sec/3600
            longitude = lon_deg + lon_min/60 + lon_sec/3600
            
            return {
                'latitude': latitude,
                'longitude': longitude,
                'lat_dms': f"{lat_deg}°{lat_min}′{lat_sec}″N",
                'lon_dms': f"{lon_deg}°{lon_min}′{lon_sec}″E"
            }
        
        return None
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符和特殊字符
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[\u200b\ufeff]', '', text)  # 移除零宽字符
        return text
    
    def extract_table_data(self, soup: BeautifulSoup) -> List[Dict]:
        """提取表格数据"""
        all_dams = []
        
        # 查找所有表格
        tables = soup.find_all('table', class_='wikitable')
        
        for table_idx, table in enumerate(tables):
            print(f"处理第 {table_idx + 1} 个表格...")
            
            # 获取表头
            headers = []
            header_row = table.find('tr')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(self.clean_text(th.get_text()))
            
            # 处理数据行
            rows = table.find_all('tr')[1:]  # 跳过表头
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < len(headers):
                    continue
                
                dam_data = {}
                
                for i, cell in enumerate(cells):
                    if i < len(headers):
                        header = headers[i]
                        cell_text = self.clean_text(cell.get_text())
                        
                        # 特殊处理坐标列
                        if '座標' in header or 'coordinate' in header.lower():
                            coords = self.extract_coordinates(cell_text)
                            if coords:
                                dam_data.update(coords)
                            dam_data[header] = cell_text
                        else:
                            dam_data[header] = cell_text
                
                if dam_data and dam_data.get('名稱'):  # 确保有名称
                    dam_data['表格序号'] = table_idx + 1
                    all_dams.append(dam_data)
        
        return all_dams
    
    def save_to_files(self, dams_data: List[Dict]):
        """保存数据到多种格式"""
        if not dams_data:
            print("没有数据可保存")
            return
        
        # 保存为CSV
        df = pd.DataFrame(dams_data)
        df.to_csv('taiwan_dams.csv', index=False, encoding='utf-8-sig')
        print(f"已保存 {len(dams_data)} 条记录到 taiwan_dams.csv")
        
        # 保存为JSON
        with open('taiwan_dams.json', 'w', encoding='utf-8') as f:
            json.dump(dams_data, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(dams_data)} 条记录到 taiwan_dams.json")
        
        # 保存坐标数据为单独的文件
        coords_data = []
        for dam in dams_data:
            if 'latitude' in dam and 'longitude' in dam:
                coords_data.append({
                    '名称': dam.get('名稱', ''),
                    '位置': dam.get('位置', ''),
                    '纬度': dam['latitude'],
                    '经度': dam['longitude'],
                    '纬度_度分秒': dam.get('lat_dms', ''),
                    '经度_度分秒': dam.get('lon_dms', '')
                })
        
        if coords_data:
            coords_df = pd.DataFrame(coords_data)
            coords_df.to_csv('taiwan_dams_coordinates.csv', index=False, encoding='utf-8-sig')
            print(f"已保存 {len(coords_data)} 个坐标到 taiwan_dams_coordinates.csv")
    
    def run(self):
        """运行提取程序"""
        print("开始提取台湾水坝数据...")
        
        soup = self.fetch_page()
        if not soup:
            return
        
        dams_data = self.extract_table_data(soup)
        
        if dams_data:
            print(f"成功提取 {len(dams_data)} 条水坝记录")
            self.save_to_files(dams_data)
            
            # 显示统计信息
            coords_count = sum(1 for dam in dams_data if 'latitude' in dam)
            print(f"其中包含坐标信息的记录: {coords_count} 条")
        else:
            print("未能提取到数据")

if __name__ == "__main__":
    extractor = TaiwanDamExtractor()
    extractor.run()
