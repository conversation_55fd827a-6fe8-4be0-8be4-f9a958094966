#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析和修复缺失的坐标数据
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
import json
from typing import List, Dict, Optional

def extract_coordinates_advanced(coord_text: str) -> Optional[Dict[str, float]]:
    """高级坐标提取函数"""
    if not coord_text:
        return None
    
    # 清理文本
    coord_text = re.sub(r'\s+', ' ', coord_text.strip())
    
    # 多种坐标格式匹配
    patterns = [
        # 标准格式：24°42′11″N 121°45′12″E
        r'(\d+)°(\d+)′(\d+)″N\s+(\d+)°(\d+)′(\d+)″E',
        # 带小数的格式：24°42′11.5″N 121°45′12.3″E  
        r'(\d+)°(\d+)′([\d.]+)″N\s+(\d+)°(\d+)′([\d.]+)″E',
        # 十进制度数格式：24.70306°N 121.75333°E
        r'([\d.]+)°N\s+([\d.]+)°E',
        # 另一种格式：N24°42′11″ E121°45′12″
        r'N(\d+)°(\d+)′(\d+)″\s+E(\d+)°(\d+)′(\d+)″',
        # 带引号的格式："24°42′11″N" "121°45′12″E"
        r'"(\d+)°(\d+)′(\d+)″N"\s+"(\d+)°(\d+)′(\d+)″E"',
        # 简化格式：24°42′N 121°45′E
        r'(\d+)°(\d+)′N\s+(\d+)°(\d+)′E',
        # 十进制格式：24.70306, 121.75333
        r'([\d.]+),\s*([\d.]+)',
        # 十进制格式带度符号：24.70306° 121.75333°
        r'([\d.]+)°\s+([\d.]+)°'
    ]
    
    for i, pattern in enumerate(patterns):
        match = re.search(pattern, coord_text)
        if match:
            groups = match.groups()
            
            try:
                if len(groups) == 2:  # 十进制格式
                    latitude = float(groups[0])
                    longitude = float(groups[1])
                elif len(groups) == 4:  # 度分格式
                    lat_deg, lat_min, lon_deg, lon_min = groups
                    latitude = float(lat_deg) + float(lat_min)/60
                    longitude = float(lon_deg) + float(lon_min)/60
                elif len(groups) == 6:  # 度分秒格式
                    lat_deg, lat_min, lat_sec, lon_deg, lon_min, lon_sec = groups
                    latitude = float(lat_deg) + float(lat_min)/60 + float(lat_sec)/3600
                    longitude = float(lon_deg) + float(lon_min)/60 + float(lon_sec)/3600
                else:
                    continue
                
                # 验证坐标范围（台湾地区）
                if 21.0 <= latitude <= 26.0 and 118.0 <= longitude <= 122.0:
                    return {
                        'latitude': latitude,
                        'longitude': longitude,
                        'lat_dms': f"{int(latitude)}°{int((latitude % 1) * 60)}′{int(((latitude % 1) * 60 % 1) * 60)}″N",
                        'lon_dms': f"{int(longitude)}°{int((longitude % 1) * 60)}′{int(((longitude % 1) * 60 % 1) * 60)}″E",
                        'pattern_used': i + 1
                    }
            except ValueError:
                continue
    
    return None

def fetch_and_parse_wikipedia():
    """重新获取并解析维基百科页面"""
    url = "https://zh.wikipedia.org/wiki/%E5%8F%B0%E7%81%A3%E6%B0%B4%E5%A3%A9%E5%88%97%E8%A1%A8"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        response = session.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找所有表格
        tables = soup.find_all('table', class_='wikitable')
        print(f"找到 {len(tables)} 个表格")
        
        all_dams = []
        
        for table_idx, table in enumerate(tables):
            print(f"\n处理第 {table_idx + 1} 个表格...")
            
            # 获取表头
            headers = []
            header_row = table.find('tr')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    header_text = re.sub(r'\s+', ' ', th.get_text().strip())
                    headers.append(header_text)
            
            print(f"表头: {headers}")
            
            # 处理数据行
            rows = table.find_all('tr')[1:]  # 跳过表头
            
            for row_idx, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) < len(headers):
                    continue
                
                dam_data = {'表格序号': table_idx + 1, '行号': row_idx + 1}
                
                for i, cell in enumerate(cells):
                    if i < len(headers):
                        header = headers[i]
                        
                        # 获取原始HTML和文本
                        cell_html = str(cell)
                        cell_text = re.sub(r'\s+', ' ', cell.get_text().strip())
                        
                        # 特殊处理坐标列
                        if '座標' in header or 'coordinate' in header.lower():
                            print(f"  处理坐标: {cell_text[:100]}...")
                            
                            # 尝试从HTML中提取更多信息
                            coord_spans = cell.find_all('span', class_='geo')
                            if coord_spans:
                                for span in coord_spans:
                                    span_text = span.get_text()
                                    print(f"    找到geo span: {span_text}")
                                    coords = extract_coordinates_advanced(span_text)
                                    if coords:
                                        dam_data.update(coords)
                                        break
                            
                            # 如果还没找到坐标，尝试从完整文本提取
                            if 'latitude' not in dam_data:
                                coords = extract_coordinates_advanced(cell_text)
                                if coords:
                                    dam_data.update(coords)
                            
                            dam_data[header] = cell_text
                        else:
                            dam_data[header] = cell_text
                
                if dam_data.get('名稱'):  # 确保有名称
                    all_dams.append(dam_data)
                    if 'latitude' in dam_data:
                        print(f"  ✓ {dam_data['名稱']}: ({dam_data['latitude']:.6f}, {dam_data['longitude']:.6f})")
                    else:
                        print(f"  ✗ {dam_data['名稱']}: 无坐标")
        
        return all_dams
        
    except Exception as e:
        print(f"获取页面失败: {e}")
        return []

def analyze_missing_coordinates():
    """分析缺失的坐标"""
    print("重新获取和分析台湾水坝数据...")
    
    # 重新获取数据
    dams_data = fetch_and_parse_wikipedia()
    
    if not dams_data:
        print("无法获取数据")
        return
    
    print(f"\n总共获取 {len(dams_data)} 条记录")
    
    # 统计坐标情况
    with_coords = [dam for dam in dams_data if 'latitude' in dam and dam['latitude']]
    without_coords = [dam for dam in dams_data if 'latitude' not in dam or not dam['latitude']]
    
    print(f"有坐标的记录: {len(with_coords)} 条")
    print(f"无坐标的记录: {len(without_coords)} 条")
    
    # 显示无坐标的记录
    if without_coords:
        print("\n无坐标的记录:")
        for dam in without_coords:
            coord_text = dam.get('座標', '')
            print(f"  - {dam.get('名稱', 'N/A')}: '{coord_text}'")
    
    # 保存完整数据
    df = pd.DataFrame(dams_data)
    df.to_csv('taiwan_dams_complete.csv', index=False, encoding='utf-8-sig')
    
    with open('taiwan_dams_complete.json', 'w', encoding='utf-8') as f:
        json.dump(dams_data, f, ensure_ascii=False, indent=2)
    
    # 保存坐标数据
    coords_data = []
    for dam in with_coords:
        coords_data.append({
            '名称': dam.get('名稱', ''),
            '位置': dam.get('位置', ''),
            '纬度': dam['latitude'],
            '经度': dam['longitude'],
            '纬度_度分秒': dam.get('lat_dms', ''),
            '经度_度分秒': dam.get('lon_dms', ''),
            '提取模式': dam.get('pattern_used', 1)
        })
    
    coords_df = pd.DataFrame(coords_data)
    coords_df.to_csv('taiwan_dams_coordinates_complete.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n已保存:")
    print(f"  - taiwan_dams_complete.csv ({len(dams_data)} 条记录)")
    print(f"  - taiwan_dams_complete.json ({len(dams_data)} 条记录)")
    print(f"  - taiwan_dams_coordinates_complete.csv ({len(coords_data)} 条坐标)")

if __name__ == "__main__":
    analyze_missing_coordinates()
